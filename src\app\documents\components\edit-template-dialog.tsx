'use client';

import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { Download, FileText, Eye, Trash2, Plus, Loader2 } from 'lucide-react';
import {
  DocumentTemplate,
  PlaceholderDefinition,
} from '@/features/document/types';
import {
  editDocumentTemplateSchema,
  EditDocumentTemplateData,
} from '@/features/document/schemas';
import { updateDocumentTemplate } from '@/features/document/actions';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Form } from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const CASE_FIELD_MAPPINGS = {
  // Basic case information
  caseNumber: 'Número de Caso',
  debtorName: 'Nombre del Deudor',
  type: 'Tipo de Caso',
  status: 'Estado del Caso',
  totalDebt: 'Deuda Total',
  creditors: 'Número de Acreedores',
  createdDate: 'Fecha de Creación',
  hearingDate: 'Fecha de Audiencia',
  phase: 'Fase del Proceso',

  // Process details
  tramite: 'Trámite',
  filingDate: 'Fecha de Radicación',
  debtorIdNumber: 'Número de Identificación del Deudor',
  convened: 'Convocado',
  attorney: 'Abogado',
  owedCapital: 'Capital Adeudado',

  // Operator information
  designatedOperator: 'Operador Designado',
  designationDate: 'Fecha de Designación',
  positionAcceptanceDate: 'Fecha de Aceptación del Cargo',

  // Process dates
  inadmissionDate: 'Fecha de Inadmisión',
  admissionDate: 'Fecha de Admisión',
  firstHearingDate: 'Fecha de Primera Audiencia',
  firstHearingTime: 'Hora de Primera Audiencia',

  // Process status
  rejection: 'Rechazo',
  withdrawal: 'Desistimiento',
  hasLegalProcesses: 'Tiene Procesos Judiciales',

  // Legal process details
  courtNumber: 'Número de Juzgado',
  city: 'Ciudad',
  processType: 'Tipo de Proceso',
  plaintiff: 'Demandante',
  judicialFileNumber: 'Número de Expediente Judicial',
  suspensionDate: 'Fecha de Suspensión',
  resultDeliveryDate: 'Fecha de Entrega de Resultado',

  // Result information
  resultType: 'Tipo de Resultado',
  resultDate: 'Fecha de Resultado',
  siccacNumber: 'Número SICCAC',
  riskCenterCommunication: 'Comunicación a Central de Riesgo',

  // Related entity information
  'debtor.name': 'Nombre del Deudor (Entidad)',
  'debtor.email': 'Email del Deudor',
  'debtor.phone': 'Teléfono del Deudor',
  'debtor.address': 'Dirección del Deudor',
  'debtor.city': 'Ciudad del Deudor',
  'debtor.department': 'Departamento del Deudor',
  'debtor.monthlyIncome': 'Ingresos Mensuales del Deudor',
  'debtor.monthlyExpenses': 'Gastos Mensuales del Deudor',

  'operator.name': 'Nombre del Operador',
  'operator.email': 'Email del Operador',
} as const;

interface EditTemplateDialogProps {
  template: DocumentTemplate;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateUpdated: (template: DocumentTemplate) => void;
}

export function EditTemplateDialog({
  template,
  open,
  onOpenChange,
  onTemplateUpdated,
}: Readonly<EditTemplateDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);
  const [placeholders, setPlaceholders] = useState<PlaceholderDefinition[]>(
    template.placeholders || [],
  );

  const form = useForm({
    resolver: zodResolver(editDocumentTemplateSchema),
    defaultValues: {
      placeholders: template.placeholders || [],
    },
  });

  // Reset form when template changes
  useEffect(() => {
    const templatePlaceholders = template.placeholders || [];
    setPlaceholders(templatePlaceholders);
    form.reset({
      placeholders: templatePlaceholders,
    });
  }, [template, form]);

  const { execute: executeUpdate, isPending } = useServerAction(
    updateDocumentTemplate,
    {
      onSuccess: ({ data }) => {
        toast.success('Plantilla actualizada exitosamente');
        onTemplateUpdated(data);
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al actualizar la plantilla');
      },
    },
  );

  const addPlaceholder = () => {
    const newPlaceholder: PlaceholderDefinition = {
      key: '',
      label: '',
    };
    const newPlaceholders = [...placeholders, newPlaceholder];
    setPlaceholders(newPlaceholders);
    form.setValue('placeholders', newPlaceholders);
    form.trigger('placeholders');
  };

  const removePlaceholder = (index: number) => {
    const newPlaceholders = placeholders.filter((_, i) => i !== index);
    setPlaceholders(newPlaceholders);
    form.setValue('placeholders', newPlaceholders);
    form.trigger('placeholders');
  };

  const updatePlaceholder = (
    index: number,
    field: keyof PlaceholderDefinition,
    value: string,
  ) => {
    const newPlaceholders = [...placeholders];
    newPlaceholders[index] = { ...newPlaceholders[index], [field]: value };
    setPlaceholders(newPlaceholders);
    form.setValue('placeholders', newPlaceholders);
    // Trigger form validation
    form.trigger('placeholders');
  };

  const onSubmit = (data: EditDocumentTemplateData) => {
    const transformedPlaceholders = (data.placeholders || [])
      .filter((p) => p.key && p.label)
      .map((p) => ({
        key: p.key,
        label: p.label,
      }));

    executeUpdate({
      id: template.id,
      placeholders: transformedPlaceholders,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] w-[90vw] overflow-y-auto sm:max-w-6xl">
        <DialogHeader>
          <DialogTitle>Editar Plantilla</DialogTitle>
          <DialogDescription>
            Modifica los detalles de la plantilla: {template.fileName}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Sección de visualización del documento */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Documento de Plantilla
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div className="flex items-center gap-3">
                    <div className="rounded-lg bg-blue-100 p-2">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{template.fileName}</p>
                      <p className="text-sm text-gray-500">
                        Archivo Word de plantilla
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/preview`;
                        window.open(url, '_blank');
                      }}
                    >
                      <Eye className="mr-2 h-4 w-4" />
                      Vista Previa
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const url = `/api/templates/${template.id}/download`;
                        window.open(url, '_blank');
                      }}
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Descargar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Información del archivo */}
            <div className="rounded-lg border bg-gray-50 p-4">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-blue-100 p-2">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium">{template.fileName}</p>
                  <p className="text-sm text-gray-500">
                    Archivo de plantilla en Google Drive
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Placeholders</h3>
                <Button type="button" onClick={addPlaceholder} size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Agregar Placeholder
                </Button>
              </div>

              {placeholders.map((placeholder, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">
                        Placeholder {index + 1}
                      </CardTitle>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removePlaceholder(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <div>
                        <label className="text-sm font-medium">
                          Campo del Caso
                        </label>
                        <Select
                          value={placeholder.key || ''}
                          onValueChange={(value) => {
                            updatePlaceholder(index, 'key', value);
                            // Auto-fill label if it's empty
                            if (!placeholder.label) {
                              updatePlaceholder(
                                index,
                                'label',
                                CASE_FIELD_MAPPINGS[
                                  value as keyof typeof CASE_FIELD_MAPPINGS
                                ] || value,
                              );
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Seleccionar campo" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(CASE_FIELD_MAPPINGS).map(
                              ([key, label]) => (
                                <SelectItem key={key} value={key}>
                                  {label}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium">
                          Etiqueta en el Documento
                        </label>
                        <Input
                          placeholder="ej: NOMBRE_DEL_CLIENTE"
                          value={placeholder.label || ''}
                          onChange={(e) =>
                            updatePlaceholder(index, 'label', e.target.value)
                          }
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          Esta etiqueta aparecerá en el documento y será
                          reemplazada con el valor del campo
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex justify-end space-x-2">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button type="submit" disabled={isPending}>
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Actualizar Plantilla
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
