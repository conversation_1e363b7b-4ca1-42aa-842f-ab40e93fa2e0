'use client';

import {
  Search,
  Eye,
  Download,
  FileText,
  Cloud,
  Folder,
  Loader2,
  MoreHorizontal,
  Wand2,
  Check,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { type Document } from '@/features/document/schemas';
import { DocumentTemplate } from '@/features/document/types';
import { getAllCases } from '@/features/case/actions';
import {
  getTemplatesFromGoogleDrive,
  syncTemplatesWithGoogleDrive,
} from '@/features/document/actions';

import { DocumentDetailsDialog } from './document-details-dialog';
import { DocumentTemplateLibrary } from './document-template-library';
import { DocumentViewerDialog } from './document-viewer-dialog';
import { GenerateDocumentDialog } from './generate-document-dialog';

import { DocumentEditor } from './document-editor';

export function DocumentManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncSuccess, setSyncSuccess] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showViewerDialog, setShowViewerDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showEditorDialog, setShowEditorDialog] = useState(false);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [cases, setCases] = useState<
    Array<{
      id: string;
      caseNumber: string;
      debtorName: string;
      type: string;
    }>
  >([]);

  const { execute: loadCases } = useServerAction(getAllCases, {
    onSuccess: ({ data }) => {
      setCases(
        data.map((c) => ({
          id: c.id,
          caseNumber: c.caseNumber,
          debtorName: c.debtorName,
          type: c.type,
        })),
      );
    },
  });

  const { execute: loadTemplates } = useServerAction(
    getTemplatesFromGoogleDrive,
    {
      onSuccess: ({ data }) => {
        setTemplates(data);
      },
    },
  );

  const { execute: syncTemplates } = useServerAction(
    syncTemplatesWithGoogleDrive,
    {
      onSuccess: ({ data }) => {
        console.log(
          `Sincronización completada: ${data.synced} plantillas, ${data.created} creadas, ${data.updated} actualizadas`,
        );
        loadTemplates({});
      },
    },
  );

  useEffect(() => {
    loadCases();
    loadTemplates({});
  }, [loadCases, loadTemplates]);

  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      name: 'Auto de Admisión - INS-2025-001',
      type: 'Auto de Admisión',
      caseId: 'INS-2025-001',
      debtorName: 'María González Pérez',
      status: 'Generado',
      createdDate: '2025-01-16',
      size: '245 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 3,
      lastAccessed: '2025-01-29 10:30',
    },
    {
      id: '2',
      name: 'Notificación Acreedores - INS-2025-001',
      type: 'Notificación a Acreedores',
      caseId: 'INS-2025-001',
      debtorName: 'María González Pérez',
      status: 'Enviado',
      createdDate: '2025-01-17',
      size: '189 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 5,
      lastAccessed: '2025-01-29 09:15',
    },
    {
      id: '3',
      name: 'Suspensión Procesos - INS-2025-001',
      type: 'Suspensión Procesos Judiciales',
      caseId: 'INS-2025-001',
      debtorName: 'María González Pérez',
      status: 'Enviado',
      createdDate: '2025-01-18',
      size: '156 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 2,
      lastAccessed: '2025-01-28 14:20',
    },
    {
      id: '4',
      name: 'Acuerdo de Pago - INS-2025-004',
      type: 'Acuerdo de Pago',
      caseId: 'INS-2025-004',
      debtorName: 'Luis Fernando Castro',
      status: 'Firmado',
      createdDate: '2025-01-25',
      size: '312 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 8,
      lastAccessed: '2025-01-29 08:45',
    },
    {
      id: '5',
      name: 'Tabla Amortización - INS-2025-004',
      type: 'Tabla de Amortización',
      caseId: 'INS-2025-004',
      debtorName: 'Luis Fernando Castro',
      status: 'Generado',
      createdDate: '2025-01-25',
      size: '89 KB',
      format: 'XLSX',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 4,
      lastAccessed: '2025-01-28 16:30',
    },
    {
      id: '6',
      name: 'Certificado REDAM - CON-2025-002',
      type: 'Certificado REDAM',
      caseId: 'CON-2025-002',
      debtorName: 'Carlos Rodríguez Silva',
      status: 'Sincronizado',
      createdDate: '2025-01-14',
      size: '1.8 MB',
      format: 'PDF',
      createdBy: 'Google Drive',
      downloadCount: 1,
      lastAccessed: '2025-01-14 15:45',
    },
    {
      id: '7',
      name: 'Extractos Bancarios - ACU-2025-003',
      type: 'Extractos Bancarios',
      caseId: 'ACU-2025-003',
      debtorName: 'Ana Martínez López',
      status: 'Sincronizado',
      createdDate: '2025-01-13',
      size: '3.2 MB',
      format: 'XLSX',
      createdBy: 'Google Drive',
      downloadCount: 0,
      lastAccessed: '2025-01-13 09:20',
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Generado':
        return 'bg-blue-100 text-blue-800';
      case 'Enviado':
        return 'bg-green-100 text-green-800';
      case 'Firmado':
        return 'bg-purple-100 text-purple-800';
      case 'Sincronizado':
        return 'bg-cyan-100 text-cyan-800';
      case 'Pendiente':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.caseId ?? '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.debtorName ?? '').toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const openDocumentViewer = (document: Document) => {
    setSelectedDocument(document);
    setShowViewerDialog(true);
  };

  const openDocumentDetails = (document: Document) => {
    setSelectedDocument(document);
    setShowDetailsDialog(true);
  };

  const editDocument = (document: Document) => {
    setEditingDocument(document);
    setShowEditorDialog(true);
  };

  const handleSaveDocument = async (content: string, changes?: string) => {
    if (!editingDocument) return;

    // Aquí se implementaría la llamada al servidor para guardar
    console.log('Guardando documento:', editingDocument.id, content, changes);

    // Actualizar el documento en el estado local
    setDocuments((docs) =>
      docs.map((doc) =>
        doc.id === editingDocument.id
          ? { ...doc, content, lastAccessed: new Date().toISOString() }
          : doc,
      ),
    );
  };

  const downloadDocument = (document: Document) => {
    setDocuments((docs) =>
      docs.map((doc) =>
        doc.id === document.id
          ? {
              ...doc,
              downloadCount: (doc.downloadCount ?? 0) + 1,
              lastAccessed: new Date().toISOString(),
            }
          : doc,
      ),
    );
    alert(`📥 Descargando: ${document.name}`);
  };

  const syncWithGoogleDrive = async () => {
    setIsSyncing(true);
    setSyncSuccess(false);
    try {
      await syncTemplates({});
      setSyncSuccess(true);
      setRefreshTrigger((prev) => prev + 1);
      setTimeout(() => setSyncSuccess(false), 2000);
    } catch (error) {
      console.error('Error syncing with Google Drive:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const handleGenerateDocument = (documentData: unknown) => {
    const data = documentData as Partial<Document>;
    const newDocument: Document = {
      id: String(documents.length + 1),
      name: data.name ?? 'Documento sin título',
      type: data.type ?? 'General',
      caseId: data.caseId ?? 'N/A',
      debtorName: data.debtorName ?? 'N/A',
      status: 'Generado',
      createdDate: new Date().toISOString().split('T')[0],
      size: data.size ?? '1 KB', // Default size for generated docs
      format: data.format ?? 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 0,
      lastAccessed: new Date().toISOString(),
    };
    setDocuments([...documents, newDocument]);
  };

  const deleteDocument = (documentId: string) => {
    if (confirm('¿Está seguro de que desea eliminar este documento?')) {
      setDocuments(documents.filter((doc) => doc.id !== documentId));
      alert('✅ Documento eliminado exitosamente');
    }
  };

  const shareDocument = (document: Document) => {
    alert(`🔗 Compartiendo: ${document.name}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Gestión de Documentos
          </h1>
          <p className="text-gray-600">
            Administre documentos legales y sincronice plantillas desde Google
            Drive
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={syncWithGoogleDrive}
            disabled={isSyncing || syncSuccess}
          >
            {isSyncing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sincronizando...
              </>
            ) : syncSuccess ? (
              <>
                <Check className="mr-2 h-4 w-4 text-green-600" />
                Sincronizado
              </>
            ) : (
              <>
                <Cloud className="mr-2 h-4 w-4" />
                Sincronizar con Drive
              </>
            )}
          </Button>
          <Button onClick={() => setShowGenerateDialog(true)}>
            <Wand2 className="mr-2 h-4 w-4" />
            Generar desde Plantilla
          </Button>
        </div>
      </div>

      <Tabs defaultValue="documents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Documentos</TabsTrigger>
          <TabsTrigger value="templates">Plantillas</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          {/* Document KPIs */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Total Documentos
                    </p>
                    <p className="text-2xl font-bold">{documents.length}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Generados Hoy
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        documents.filter(
                          (doc) =>
                            doc.createdDate ===
                            new Date().toISOString().split('T')[0],
                        ).length
                      }
                    </p>
                  </div>
                  <Folder className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Pendientes
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        documents.filter((doc) => doc.status === 'Pendiente')
                          .length
                      }
                    </p>
                  </div>
                  <FileText className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Documentos Generados</CardTitle>
                  <CardDescription>
                    Lista de documentos creados en el sistema
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Buscar documentos..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-80 pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Documento</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Caso</TableHead>
                      <TableHead>Deudor</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Tamaño</TableHead>
                      <TableHead>Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDocuments.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{doc.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{doc.type}</TableCell>
                        <TableCell>{doc.caseId}</TableCell>
                        <TableCell>{doc.debtorName}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(doc.status)}>
                            {doc.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(
                            doc.createdDate ?? Date.now(),
                          ).toLocaleDateString('es-CO')}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {doc.size} ({doc.format})
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openDocumentViewer(doc)}
                              title="Ver documento"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => downloadDocument(doc)}
                              title="Descargar documento"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => openDocumentDetails(doc)}
                                >
                                  Ver detalles
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => editDocument(doc)}
                                >
                                  Editar documento
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => shareDocument(doc)}
                                >
                                  Compartir
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => deleteDocument(doc.id)}
                                  className="text-red-600"
                                >
                                  Eliminar
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <DocumentTemplateLibrary
            templates={templates}
            onTemplateUpdated={(template) => {
              setTemplates((prev) =>
                prev.map((t) => (t.id === template.id ? template : t)),
              );
              console.log('Plantilla actualizada:', template);
            }}
            onTemplateDeleted={(templateId) => {
              setTemplates((prev) => prev.filter((t) => t.id !== templateId));
              console.log('Plantilla eliminada:', templateId);
            }}
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>
      </Tabs>

      <GenerateDocumentDialog
        open={showGenerateDialog}
        onOpenChange={setShowGenerateDialog}
        templates={templates}
        cases={cases}
      />

      {selectedDocument && (
        <>
          <DocumentViewerDialog
            open={showViewerDialog}
            onOpenChange={setShowViewerDialog}
            document={selectedDocument}
          />
          <DocumentDetailsDialog
            open={showDetailsDialog}
            onOpenChange={setShowDetailsDialog}
            document={selectedDocument}
            onDownload={downloadDocument}
            onShare={shareDocument}
            onDelete={deleteDocument}
          />
        </>
      )}

      {editingDocument && (
        <Dialog open={showEditorDialog} onOpenChange={setShowEditorDialog}>
          <DialogContent className="max-h-[95vh] max-w-7xl overflow-hidden">
            <DialogHeader>
              <DialogTitle>
                Editar Documento: {editingDocument.name}
              </DialogTitle>
              <DialogDescription>
                Edite el contenido del documento usando el editor WYSIWYG
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-hidden">
              <DocumentEditor
                documentId={editingDocument.id}
                initialContent={''}
                onSave={handleSaveDocument}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
