'use client';

import {
  Search,
  Eye,
  Download,
  FileText,
  Cloud,
  Folder,
  Loader2,
  MoreHorizontal,
  Wand2,
  Check,
  Copy,
  Info,
  FileDown,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { type Document } from '@/features/document/schemas';
import { DocumentTemplate } from '@/features/document/types';
import { getAllCases } from '@/features/case/actions';
import { toast } from 'sonner';
import {
  getTemplatesFromGoogleDrive,
  syncTemplatesWithGoogleDrive,
  generateDocumentWithPlaceholders,
} from '@/features/document/actions';

import { DocumentDetailsDialog } from './document-details-dialog';
import { DocumentTemplateLibrary } from './document-template-library';
import { DocumentViewerDialog } from './document-viewer-dialog';

import { DocumentEditor } from './document-editor';

const AVAILABLE_PLACEHOLDERS = [
  {
    key: 'case.caseNumber',
    label: 'Número de Caso',
    placeholder: '{{numeroCaso}}',
  },
  {
    key: 'case.debtorName',
    label: 'Nombre del Deudor',
    placeholder: '{{nombreDeudor}}',
  },
  { key: 'case.type', label: 'Tipo de Caso', placeholder: '{{tipoCaso}}' },
  {
    key: 'case.status',
    label: 'Estado del Caso',
    placeholder: '{{estadoCaso}}',
  },
  {
    key: 'case.createdDate',
    label: 'Fecha de Creación',
    placeholder: '{{fechaCreacion}}',
  },
  {
    key: 'case.hearingDate',
    label: 'Fecha de Audiencia',
    placeholder: '{{fechaAudiencia}}',
  },
  { key: 'case.phase', label: 'Fase del Caso', placeholder: '{{faseCaso}}' },
  { key: 'case.tramite', label: 'Trámite', placeholder: '{{tramite}}' },
  {
    key: 'case.filingDate',
    label: 'Fecha de Radicación',
    placeholder: '{{fechaRadicacion}}',
  },
  { key: 'case.attorney', label: 'Abogado', placeholder: '{{abogado}}' },
  {
    key: 'case.owedCapital',
    label: 'Capital Adeudado',
    placeholder: '{{capitalAdeudado}}',
  },
  {
    key: 'debtor.name',
    label: 'Nombre Completo del Deudor',
    placeholder: '{{deudorNombre}}',
  },
  {
    key: 'debtor.email',
    label: 'Email del Deudor',
    placeholder: '{{deudorEmail}}',
  },
  {
    key: 'debtor.phone',
    label: 'Teléfono del Deudor',
    placeholder: '{{deudorTelefono}}',
  },
  {
    key: 'debtor.address',
    label: 'Dirección del Deudor',
    placeholder: '{{deudorDireccion}}',
  },
  {
    key: 'debtor.city',
    label: 'Ciudad del Deudor',
    placeholder: '{{deudorCiudad}}',
  },
  {
    key: 'debtor.department',
    label: 'Departamento del Deudor',
    placeholder: '{{deudorDepartamento}}',
  },
  {
    key: 'debtor.monthlyIncome',
    label: 'Ingresos Mensuales del Deudor',
    placeholder: '{{deudorIngresosMensuales}}',
  },
  {
    key: 'debtor.monthlyExpenses',
    label: 'Gastos Mensuales del Deudor',
    placeholder: '{{deudorGastosMensuales}}',
  },
  {
    key: 'operator.name',
    label: 'Nombre del Operador',
    placeholder: '{{operadorNombre}}',
  },
  {
    key: 'operator.email',
    label: 'Email del Operador',
    placeholder: '{{operadorEmail}}',
  },
];

export function DocumentManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncSuccess, setSyncSuccess] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showGenerateDialog, setShowGenerateDialog] = useState(false);
  const [showViewerDialog, setShowViewerDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showEditorDialog, setShowEditorDialog] = useState(false);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [templates, setTemplates] = useState<DocumentTemplate[]>([]);
  const [showPlaceholdersDialog, setShowPlaceholdersDialog] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<DocumentTemplate | null>(null);
  const [selectedCase, setSelectedCase] = useState<string>('');
  const [outputFormat, setOutputFormat] = useState<'docx' | 'pdf'>('docx');
  const [isGenerating, setIsGenerating] = useState(false);
  const [cases, setCases] = useState<
    Array<{
      id: string;
      caseNumber: string;
      debtorName: string;
      type: string;
      status: string;
    }>
  >([]);

  const { execute: loadCases } = useServerAction(getAllCases, {
    onSuccess: ({ data }) => {
      setCases(
        data.map((c) => ({
          id: c.id,
          caseNumber: c.caseNumber,
          debtorName: c.debtorName,
          type: c.type,
          status: c.status,
        })),
      );
    },
  });

  const { execute: loadTemplates } = useServerAction(
    getTemplatesFromGoogleDrive,
    {
      onSuccess: ({ data }) => {
        setTemplates(data);
      },
    },
  );

  const { execute: syncTemplates } = useServerAction(
    syncTemplatesWithGoogleDrive,
    {
      onSuccess: ({ data }) => {
        console.log(
          `Sincronización completada: ${data.synced} plantillas, ${data.created} creadas, ${data.updated} actualizadas`,
        );
        loadTemplates({});
      },
    },
  );

  const { execute: generateDocument } = useServerAction(
    generateDocumentWithPlaceholders,
    {
      onSuccess: (result) => {
        setIsGenerating(false);
        setShowGenerateDialog(false);
        toast.success('Documento generado exitosamente');

        // Download the generated document
        const docData = result.data as {
          buffer?: ArrayBuffer;
          fileName?: string;
          mimeType?: string;
        } | null;
        if (docData?.buffer && docData?.fileName && docData?.mimeType) {
          const blob = new Blob([docData.buffer], {
            type: docData.mimeType,
          });
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = docData.fileName;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          URL.revokeObjectURL(url);
        }
      },
      onError: ({ err }) => {
        setIsGenerating(false);
        toast.error(err.message);
      },
    },
  );

  useEffect(() => {
    loadCases();
    loadTemplates({});
  }, [loadCases, loadTemplates]);

  const handleGenerateDocument = () => {
    if (!selectedTemplate || !selectedCase) {
      toast.error('Por favor selecciona una plantilla y un caso');
      return;
    }

    setIsGenerating(true);
    generateDocument({
      templateId: selectedTemplate.id,
      caseId: selectedCase,
      format: outputFormat,
    });
  };

  const copyPlaceholderToClipboard = (placeholder: string) => {
    navigator.clipboard.writeText(placeholder);
    toast.success(`Placeholder ${placeholder} copiado al portapapeles`);
  };

  const [documents, setDocuments] = useState<Document[]>([
    {
      id: '1',
      name: 'Auto de Admisión - INS-2025-001',
      type: 'Auto de Admisión',
      caseId: 'INS-2025-001',
      debtorName: 'María González Pérez',
      status: 'Generado',
      createdDate: '2025-01-16',
      size: '245 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 3,
      lastAccessed: '2025-01-29 10:30',
    },
    {
      id: '2',
      name: 'Notificación Acreedores - INS-2025-001',
      type: 'Notificación a Acreedores',
      caseId: 'INS-2025-001',
      debtorName: 'María González Pérez',
      status: 'Enviado',
      createdDate: '2025-01-17',
      size: '189 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 5,
      lastAccessed: '2025-01-29 09:15',
    },
    {
      id: '3',
      name: 'Suspensión Procesos - INS-2025-001',
      type: 'Suspensión Procesos Judiciales',
      caseId: 'INS-2025-001',
      debtorName: 'María González Pérez',
      status: 'Enviado',
      createdDate: '2025-01-18',
      size: '156 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 2,
      lastAccessed: '2025-01-28 14:20',
    },
    {
      id: '4',
      name: 'Acuerdo de Pago - INS-2025-004',
      type: 'Acuerdo de Pago',
      caseId: 'INS-2025-004',
      debtorName: 'Luis Fernando Castro',
      status: 'Firmado',
      createdDate: '2025-01-25',
      size: '312 KB',
      format: 'PDF',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 8,
      lastAccessed: '2025-01-29 08:45',
    },
    {
      id: '5',
      name: 'Tabla Amortización - INS-2025-004',
      type: 'Tabla de Amortización',
      caseId: 'INS-2025-004',
      debtorName: 'Luis Fernando Castro',
      status: 'Generado',
      createdDate: '2025-01-25',
      size: '89 KB',
      format: 'XLSX',
      createdBy: 'Beatriz Helena Malavera',
      downloadCount: 4,
      lastAccessed: '2025-01-28 16:30',
    },
    {
      id: '6',
      name: 'Certificado REDAM - CON-2025-002',
      type: 'Certificado REDAM',
      caseId: 'CON-2025-002',
      debtorName: 'Carlos Rodríguez Silva',
      status: 'Sincronizado',
      createdDate: '2025-01-14',
      size: '1.8 MB',
      format: 'PDF',
      createdBy: 'Google Drive',
      downloadCount: 1,
      lastAccessed: '2025-01-14 15:45',
    },
    {
      id: '7',
      name: 'Extractos Bancarios - ACU-2025-003',
      type: 'Extractos Bancarios',
      caseId: 'ACU-2025-003',
      debtorName: 'Ana Martínez López',
      status: 'Sincronizado',
      createdDate: '2025-01-13',
      size: '3.2 MB',
      format: 'XLSX',
      createdBy: 'Google Drive',
      downloadCount: 0,
      lastAccessed: '2025-01-13 09:20',
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Generado':
        return 'bg-blue-100 text-blue-800';
      case 'Enviado':
        return 'bg-green-100 text-green-800';
      case 'Firmado':
        return 'bg-purple-100 text-purple-800';
      case 'Sincronizado':
        return 'bg-cyan-100 text-cyan-800';
      case 'Pendiente':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.caseId ?? '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.debtorName ?? '').toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const openDocumentViewer = (document: Document) => {
    setSelectedDocument(document);
    setShowViewerDialog(true);
  };

  const openDocumentDetails = (document: Document) => {
    setSelectedDocument(document);
    setShowDetailsDialog(true);
  };

  const editDocument = (document: Document) => {
    setEditingDocument(document);
    setShowEditorDialog(true);
  };

  const handleSaveDocument = async (content: string, changes?: string) => {
    if (!editingDocument) return;

    // Aquí se implementaría la llamada al servidor para guardar
    console.log('Guardando documento:', editingDocument.id, content, changes);

    // Actualizar el documento en el estado local
    setDocuments((docs) =>
      docs.map((doc) =>
        doc.id === editingDocument.id
          ? { ...doc, content, lastAccessed: new Date().toISOString() }
          : doc,
      ),
    );
  };

  const downloadDocument = (document: Document) => {
    setDocuments((docs) =>
      docs.map((doc) =>
        doc.id === document.id
          ? {
              ...doc,
              downloadCount: (doc.downloadCount ?? 0) + 1,
              lastAccessed: new Date().toISOString(),
            }
          : doc,
      ),
    );
    alert(`📥 Descargando: ${document.name}`);
  };

  const syncWithGoogleDrive = async () => {
    setIsSyncing(true);
    setSyncSuccess(false);
    try {
      await syncTemplates({});
      setSyncSuccess(true);
      setRefreshTrigger((prev) => prev + 1);
      setTimeout(() => setSyncSuccess(false), 2000);
    } catch (error) {
      console.error('Error syncing with Google Drive:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const deleteDocument = (documentId: string) => {
    if (confirm('¿Está seguro de que desea eliminar este documento?')) {
      setDocuments(documents.filter((doc) => doc.id !== documentId));
      alert('✅ Documento eliminado exitosamente');
    }
  };

  const shareDocument = (document: Document) => {
    alert(`🔗 Compartiendo: ${document.name}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Gestión de Documentos
          </h1>
          <p className="text-gray-600">
            Administre documentos legales y sincronice plantillas desde Google
            Drive
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={syncWithGoogleDrive}
            disabled={isSyncing || syncSuccess}
          >
            {(() => {
              if (isSyncing) {
                return (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sincronizando...
                  </>
                );
              }
              if (syncSuccess) {
                return (
                  <>
                    <Check className="mr-2 h-4 w-4 text-green-600" />
                    Sincronizado
                  </>
                );
              }
              return (
                <>
                  <Cloud className="mr-2 h-4 w-4" />
                  Sincronizar con Drive
                </>
              );
            })()}
          </Button>
          <Button onClick={() => setShowPlaceholdersDialog(true)}>
            <Info className="mr-2 h-4 w-4" />
            Ver Placeholders
          </Button>
          <Button onClick={() => setShowGenerateDialog(true)}>
            <Wand2 className="mr-2 h-4 w-4" />
            Generar desde Plantilla
          </Button>
        </div>
      </div>

      <Tabs defaultValue="documents" className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Documentos</TabsTrigger>
          <TabsTrigger value="templates">Plantillas</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          {/* Document KPIs */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Total Documentos
                    </p>
                    <p className="text-2xl font-bold">{documents.length}</p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Generados Hoy
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        documents.filter(
                          (doc) =>
                            doc.createdDate ===
                            new Date().toISOString().split('T')[0],
                        ).length
                      }
                    </p>
                  </div>
                  <Folder className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Pendientes
                    </p>
                    <p className="text-2xl font-bold">
                      {
                        documents.filter((doc) => doc.status === 'Pendiente')
                          .length
                      }
                    </p>
                  </div>
                  <FileText className="h-8 w-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Documentos Generados</CardTitle>
                  <CardDescription>
                    Lista de documentos creados en el sistema
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Buscar documentos..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-80 pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Documento</TableHead>
                      <TableHead>Tipo</TableHead>
                      <TableHead>Caso</TableHead>
                      <TableHead>Deudor</TableHead>
                      <TableHead>Estado</TableHead>
                      <TableHead>Fecha</TableHead>
                      <TableHead>Tamaño</TableHead>
                      <TableHead>Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredDocuments.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{doc.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>{doc.type}</TableCell>
                        <TableCell>{doc.caseId}</TableCell>
                        <TableCell>{doc.debtorName}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(doc.status)}>
                            {doc.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(
                            doc.createdDate ?? Date.now(),
                          ).toLocaleDateString('es-CO')}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {doc.size} ({doc.format})
                          </span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openDocumentViewer(doc)}
                              title="Ver documento"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => downloadDocument(doc)}
                              title="Descargar documento"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() => openDocumentDetails(doc)}
                                >
                                  Ver detalles
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => editDocument(doc)}
                                >
                                  Editar documento
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => shareDocument(doc)}
                                >
                                  Compartir
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => deleteDocument(doc.id)}
                                  className="text-red-600"
                                >
                                  Eliminar
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates">
          <DocumentTemplateLibrary
            templates={templates}
            onTemplateUpdated={(template) => {
              setTemplates((prev) =>
                prev.map((t) => (t.id === template.id ? template : t)),
              );
              console.log('Plantilla actualizada:', template);
            }}
            onTemplateDeleted={(templateId) => {
              setTemplates((prev) => prev.filter((t) => t.id !== templateId));
              console.log('Plantilla eliminada:', templateId);
            }}
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>
      </Tabs>

      {/* Placeholders Dialog */}
      <Dialog
        open={showPlaceholdersDialog}
        onOpenChange={setShowPlaceholdersDialog}
      >
        <DialogContent className="max-h-[80vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Placeholders Disponibles</DialogTitle>
            <DialogDescription>
              Estos son los placeholders que puedes usar en tus documentos. Haz
              clic en cualquiera para copiarlo al portapapeles.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {AVAILABLE_PLACEHOLDERS.map((item) => (
              <Button
                key={item.key}
                variant="outline"
                className="h-auto justify-start p-3 text-left"
                onClick={() => copyPlaceholderToClipboard(item.placeholder)}
              >
                <div className="flex w-full items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{item.label}</p>
                    <p className="font-mono text-xs text-gray-500">
                      {item.placeholder}
                    </p>
                  </div>
                  <Copy className="ml-2 h-4 w-4 text-gray-400" />
                </div>
              </Button>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Generate Document Dialog */}
      <Dialog open={showGenerateDialog} onOpenChange={setShowGenerateDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Generar Documento</DialogTitle>
            <DialogDescription>
              Selecciona una plantilla y un caso para generar el documento.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="template">Plantilla</Label>
              <Select
                value={selectedTemplate?.id || ''}
                onValueChange={(value) => {
                  const template = templates.find((t) => t.id === value);
                  setSelectedTemplate(template || null);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una plantilla" />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.fileName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="case">Caso</Label>
              <Select value={selectedCase} onValueChange={setSelectedCase}>
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona un caso" />
                </SelectTrigger>
                <SelectContent>
                  {cases.map((caseItem) => (
                    <SelectItem key={caseItem.id} value={caseItem.id}>
                      {caseItem.caseNumber} - {caseItem.debtorName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="format">Formato de Salida</Label>
              <Select
                value={outputFormat}
                onValueChange={(value: 'docx' | 'pdf') =>
                  setOutputFormat(value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="docx">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-4 w-4" />
                      Word (.docx)
                    </div>
                  </SelectItem>
                  <SelectItem value="pdf">
                    <div className="flex items-center">
                      <FileDown className="mr-2 h-4 w-4" />
                      PDF (.pdf)
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowGenerateDialog(false)}
                disabled={isGenerating}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleGenerateDocument}
                disabled={isGenerating || !selectedTemplate || !selectedCase}
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generando...
                  </>
                ) : (
                  <>
                    <Wand2 className="mr-2 h-4 w-4" />
                    Generar
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {selectedDocument && (
        <>
          <DocumentViewerDialog
            open={showViewerDialog}
            onOpenChange={setShowViewerDialog}
            document={selectedDocument}
          />
          <DocumentDetailsDialog
            open={showDetailsDialog}
            onOpenChange={setShowDetailsDialog}
            document={selectedDocument}
            onDownload={downloadDocument}
            onShare={shareDocument}
            onDelete={deleteDocument}
          />
        </>
      )}

      {editingDocument && (
        <Dialog open={showEditorDialog} onOpenChange={setShowEditorDialog}>
          <DialogContent className="max-h-[95vh] max-w-7xl overflow-hidden">
            <DialogHeader>
              <DialogTitle>
                Editar Documento: {editingDocument.name}
              </DialogTitle>
              <DialogDescription>
                Edite el contenido del documento usando el editor WYSIWYG
              </DialogDescription>
            </DialogHeader>
            <div className="flex-1 overflow-hidden">
              <DocumentEditor
                documentId={editingDocument.id}
                initialContent={''}
                onSave={handleSaveDocument}
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
