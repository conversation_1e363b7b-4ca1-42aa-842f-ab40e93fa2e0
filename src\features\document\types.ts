export type {
  Document,
  DocumentWithCase,
  DocumentStats,
  CreateDocumentData,
  UpdateDocumentData,
  DeleteDocumentData,
  DocumentFilter,
  DocumentTemplate,
  GenerateDocumentFromTemplateData,
  UpdateDocumentContentData,
} from './schemas';

// Tipos adicionales para el manejo de documentos Word
export interface PlaceholderDefinition {
  key: string;
  label: string;
}

export interface DocumentGenerationContext {
  case: {
    id: string;
    caseNumber: string;
    debtorName: string;
    type: string;
    status: string;
    totalDebt: number;
    creditors: number;
    createdDate: Date;
    hearingDate?: Date;
    phase?: string;
  };
  debtor: {
    id: string;
    name: string;
    documentNumber: string;
    documentType: string;
    email?: string;
    phone?: string;
    address?: string;
  };
  operator: {
    id: string;
    name: string;
    email: string;
  };
  customValues?: Record<string, string | number | boolean>;
}

export interface WordDocumentData {
  content: Buffer;
  filename: string;
  mimeType: string;
}

export interface DocumentEditorProps {
  documentId: string;
  initialContent?: string;
  onSave: (content: string, changes?: string) => Promise<void>;
  readOnly?: boolean;
}

export type GoogleDriveFolder = {
  id: string;
  name: string;
  createdTime?: string;
  modifiedTime?: string;
};

export type GoogleDriveFile = {
  id: string;
  name: string;
  mimeType?: string;
  size?: string;
  createdTime?: string;
  modifiedTime?: string;
  parents?: string[];
};

export type GoogleDriveFolderContent = {
  folders: GoogleDriveFolder[];
  files: GoogleDriveFile[];
};
