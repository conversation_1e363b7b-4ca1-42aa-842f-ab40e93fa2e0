'use client';

import { useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { Download, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { DocumentTemplate } from '@/features/document/types';
import { generateDocumentFromTemplate } from '@/features/document/actions';
import { z } from 'zod';

const generateDocumentSchema = z.object({
  templateId: z.string().min(1, 'Selecciona una plantilla'),
  caseId: z.string().min(1, 'Selecciona un caso'),
});

type GenerateDocumentData = z.infer<typeof generateDocumentSchema>;

interface GenerateDocumentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  templates: DocumentTemplate[];
  cases: Array<{
    id: string;
    caseNumber: string;
    debtorName: string;
    type: string;
    status: string;
  }>;
}

export function GenerateDocumentDialog({
  open,
  onOpenChange,
  templates,
  cases,
}: Readonly<GenerateDocumentDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);

  const { execute: executeGenerate, isPending } = useServerAction(
    generateDocumentFromTemplate,
    {
      onSuccess: ({ data }) => {
        toast.success('Documento generado exitosamente');
        if (data.url) {
          window.open(data.url, '_blank');
        }
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al generar el documento');
      },
    },
  );

  const form = useForm<GenerateDocumentData>({
    resolver: zodResolver(generateDocumentSchema),
    defaultValues: {
      templateId: '',
      caseId: '',
    },
  });

  const onSubmit = (data: GenerateDocumentData) => {
    executeGenerate(data);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Generar Documento desde Plantilla</DialogTitle>
          <DialogDescription>
            Seleccione una plantilla y complete los campos para generar un nuevo
            documento
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="templateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Plantilla</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[200px]">
                          <SelectValue placeholder="Seleccione una plantilla" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {templates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            <span>{template.fileName}</span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="caseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Caso</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-[200px]">
                          <SelectValue placeholder="Seleccione un caso" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {cases.map((case_) => (
                          <SelectItem key={case_.id} value={case_.id}>
                            {case_.caseNumber} - {case_.debtorName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  ref={closeRef}
                  disabled={isPending}
                >
                  Cancelar
                </Button>
              </DialogClose>
              <Button
                type="submit"
                disabled={isPending}
                className="flex items-center space-x-2"
              >
                {isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Generando...</span>
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    <span>Generar Documento</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
