# Guía de Generación de Documentos con Placeholders

## Resumen
El sistema de generación de documentos permite crear documentos personalizados usando plantillas de Word con placeholders que se reemplazan automáticamente con datos de los casos.

## Características Implementadas

### 1. Sistema de Placeholders en Español
- **Placeholders predefinidos**: Lista completa de placeholders disponibles en español
- **Interfaz visual**: Botones clickeables para copiar placeholders al portapapeles
- **Categorización**: Organizados por tipo (caso, deudor, operador)

### 2. Generación de Documentos
- **Formatos soportados**: Word (.docx) y PDF (.pdf)
- **Descarga automática**: Los documentos se descargan automáticamente al generarse
- **Integración con base de datos**: Los documentos generados se guardan en la base de datos

### 3. Interfaz de Usuario Mejorada
- **Diálogo de placeholders**: Muestra todos los placeholders disponibles
- **Diálogo de generación**: Permite seleccionar plantilla, caso y formato
- **Feedback visual**: Indicadores de carga y mensajes de éxito/error

## Placeholders Disponibles

### Datos del Caso
- `{{numeroCaso}}` - Número de Caso
- `{{nombreDeudor}}` - Nombre del Deudor
- `{{tipoCaso}}` - Tipo de Caso
- `{{estadoCaso}}` - Estado del Caso
- `{{fechaCreacion}}` - Fecha de Creación
- `{{fechaAudiencia}}` - Fecha de Audiencia
- `{{faseCaso}}` - Fase del Caso
- `{{tramite}}` - Trámite
- `{{fechaRadicacion}}` - Fecha de Radicación
- `{{abogado}}` - Abogado
- `{{capitalAdeudado}}` - Capital Adeudado

### Datos del Deudor
- `{{deudorNombre}}` - Nombre Completo del Deudor
- `{{deudorEmail}}` - Email del Deudor
- `{{deudorTelefono}}` - Teléfono del Deudor
- `{{deudorDireccion}}` - Dirección del Deudor
- `{{deudorCiudad}}` - Ciudad del Deudor
- `{{deudorDepartamento}}` - Departamento del Deudor
- `{{deudorIngresosMensuales}}` - Ingresos Mensuales del Deudor
- `{{deudorGastosMensuales}}` - Gastos Mensuales del Deudor

### Datos del Operador
- `{{operadorNombre}}` - Nombre del Operador
- `{{operadorEmail}}` - Email del Operador

## Cómo Usar el Sistema

### Paso 1: Crear Plantillas en Word
1. Crea un documento de Word (.docx)
2. Inserta los placeholders donde quieras que aparezcan los datos
3. Usa exactamente los placeholders mostrados en la interfaz (ej: `{{numeroCaso}}`)
4. Guarda el documento

### Paso 2: Subir a Google Drive
1. Sube tu plantilla a Google Drive
2. En la aplicación, ve a la sección "Documentos"
3. Haz clic en "Sincronizar con Drive" para importar las plantillas

### Paso 3: Ver Placeholders Disponibles
1. Haz clic en "Ver Placeholders"
2. Revisa todos los placeholders disponibles
3. Haz clic en cualquier placeholder para copiarlo al portapapeles
4. Úsalos en tus plantillas de Word

### Paso 4: Generar Documentos
1. Haz clic en "Generar desde Plantilla"
2. Selecciona la plantilla que quieres usar
3. Selecciona el caso para el cual generar el documento
4. Elige el formato de salida (Word o PDF)
5. Haz clic en "Generar"
6. El documento se descargará automáticamente

## Tecnologías Utilizadas

### Backend
- **docxtemplater**: Biblioteca para reemplazar placeholders en documentos Word
- **pizzip**: Manejo de archivos ZIP (formato interno de .docx)
- **libreoffice-convert**: Conversión de Word a PDF
- **Prisma**: ORM para base de datos
- **zsa**: Server actions con validación

### Frontend
- **React**: Interfaz de usuario
- **Next.js**: Framework full-stack
- **Tailwind CSS**: Estilos
- **shadcn/ui**: Componentes de UI
- **Sonner**: Notificaciones toast

## Estructura de Archivos

```
src/
├── features/document/
│   ├── actions.ts                 # Server actions para generación
│   ├── schemas.ts                 # Esquemas de validación
│   └── types.ts                   # Tipos TypeScript
├── app/documents/
│   └── components/
│       ├── document-management.tsx # Componente principal
│       ├── document-template-library.tsx
│       └── ...
└── prisma/
    └── schema.prisma              # Esquema de base de datos
```

## Próximas Mejoras

### Funcionalidades Pendientes
1. **Integración completa con Google Drive API**
   - Descarga automática de plantillas
   - Subida de documentos generados

2. **Editor de plantillas en línea**
   - Edición de placeholders desde la interfaz
   - Vista previa en tiempo real

3. **Plantillas más complejas**
   - Soporte para tablas dinámicas
   - Imágenes y gráficos
   - Bucles y condicionales

4. **Historial de documentos**
   - Versiones de documentos
   - Auditoría de cambios

### Optimizaciones
1. **Rendimiento**
   - Cache de plantillas
   - Generación asíncrona para documentos grandes

2. **Seguridad**
   - Validación de plantillas
   - Sanitización de datos

3. **Experiencia de usuario**
   - Drag & drop para plantillas
   - Editor WYSIWYG

## Solución de Problemas

### Error: "Plantilla no encontrada"
- Verifica que la plantilla esté sincronizada con Google Drive
- Asegúrate de que el archivo sea un .docx válido

### Error: "Error al convertir a PDF"
- Verifica que LibreOffice esté instalado en el servidor
- Intenta generar en formato Word primero

### Placeholders no se reemplazan
- Verifica que uses exactamente los placeholders mostrados en la interfaz
- Asegúrate de que no haya espacios extra en los placeholders
- Los placeholders deben estar entre dobles llaves: `{{placeholder}}`

## Soporte
Para reportar problemas o solicitar nuevas funcionalidades, contacta al equipo de desarrollo.
