'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';
import { z } from 'zod';

import prisma from '@/lib/prisma';

import {
  createDocumentSchema,
  updateDocumentSchema,
  deleteDocumentSchema,
  documentFilterSchema,
  documentStatsSchema,
  getDocumentsSchema,
  getDocumentByIdSchema,
  createDocumentOutputSchema,
  updateDocumentOutputSchema,
  deleteDocumentOutputSchema,
  generateDocumentFromTemplateSchema,
  updateDocumentContentSchema,
  documentTemplateSchema,
} from './schemas';

export const getDocuments = createServerAction()
  .input(documentFilterSchema.optional())
  .output(getDocumentsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.document.findMany({
      where: {
        ...(filters?.caseId && { caseId: filters.caseId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            {
              case: {
                caseNumber: { contains: filters.search, mode: 'insensitive' },
              },
            },
            {
              case: {
                debtorName: { contains: filters.search, mode: 'insensitive' },
              },
            },
          ],
        }),
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });
  });

export const getDocumentById = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID del documento es requerido') }),
  )
  .output(getDocumentByIdSchema)
  .handler(async ({ input: { id } }) => {
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('Documento no encontrado');
    }

    return document;
  });

export const createDocument = createServerAction()
  .input(createDocumentSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const document = await prisma.document.create({
      data: {
        ...data,
        status: data.status ?? 'PENDIENTE',
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocument = createServerAction()
  .input(updateDocumentSchema)
  .output(updateDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const { id: documentId, ...updateData } = data;
    const document = await prisma.document.update({
      where: { id: documentId },
      data: updateData,
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const deleteDocument = createServerAction()
  .input(deleteDocumentSchema)
  .output(deleteDocumentOutputSchema)
  .handler(async ({ input: { id } }) => {
    const deletedDocument = await prisma.document.delete({
      where: { id },
    });

    revalidatePath('/documents');
    return deletedDocument;
  });

// Acciones para plantillas de documentos
export const getDocumentTemplates = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const templates = await prisma.documentTemplate.findMany({
      orderBy: {
        fileName: 'asc',
      },
    });

    return templates.map((template) => ({
      ...template,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    }));
  });

export const updateDocumentTemplate = createServerAction()
  .input(
    z.object({
      id: z.string().min(1, 'El ID de la plantilla es requerido'),
      placeholders: z
        .array(
          z.object({
            key: z.string().min(1, 'La clave es requerida'),
            label: z.string().min(1, 'La etiqueta es requerida'),
            type: z.enum(['text', 'number', 'date', 'email']),
            required: z.boolean(),
            description: z.string().optional(),
          }),
        )
        .optional(),
    }),
  )
  .output(documentTemplateSchema)
  .handler(async ({ input: data }) => {
    const { id, placeholders } = data;
    const { googleDriveService } = await import('@/lib/google-drive');

    const fileInfo = await googleDriveService.getFileInfo(id);

    if (!fileInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    let template = await prisma.documentTemplate.findFirst({
      where: { googleDriveId: id },
    });

    if (template) {
      template = await prisma.documentTemplate.update({
        where: { id: template.id },
        data: { placeholders: placeholders || [] },
      });
    } else {
      template = await prisma.documentTemplate.create({
        data: {
          googleDriveId: id,
          fileName: fileInfo.name || 'template.docx',
          mimeType: fileInfo.mimeType || 'application/octet-stream',
          placeholders: placeholders || [],
          createdAt: fileInfo.createdTime
            ? new Date(fileInfo.createdTime)
            : new Date(),
          updatedAt: fileInfo.modifiedTime
            ? new Date(fileInfo.modifiedTime)
            : new Date(),
        },
      });
    }

    revalidatePath('/documents/templates');
    return {
      id: template.googleDriveId,
      googleDriveId: template.googleDriveId,
      fileName: template.fileName,
      mimeType: template.mimeType,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
      createdAt: template.createdAt,
      updatedAt: template.updatedAt,
    };
  });

export const downloadDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(
    z.object({
      buffer: z.any(),
      fileName: z.string(),
      mimeType: z.string(),
    }),
  )
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const fileInfo = await googleDriveService.getFileInfo(id);

    if (!fileInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    const buffer = await googleDriveService.downloadFile(id);

    return {
      buffer,
      fileName: fileInfo.name || 'template.docx',
      mimeType: fileInfo.mimeType || 'application/octet-stream',
    };
  });

export const deleteDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    try {
      await googleDriveService.deleteFile(id);
    } catch (error) {
      console.error('Error eliminando archivo de Google Drive:', error);
      throw new Error('Error al eliminar la plantilla de Google Drive');
    }

    const existingTemplate = await prisma.documentTemplate.findFirst({
      where: { googleDriveId: id },
    });

    if (existingTemplate) {
      await prisma.documentTemplate.delete({
        where: { id: existingTemplate.id },
      });
    }

    revalidatePath('/documents/templates');
    return { success: true };
  });

export const getGoogleDriveFoldersAndFiles = createServerAction()
  .input(z.object({ folderId: z.string().optional() }).optional())
  .output(
    z.object({
      folders: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          createdTime: z.string().optional(),
          modifiedTime: z.string().optional(),
        }),
      ),
      files: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          mimeType: z.string().optional(),
          size: z.string().optional(),
          createdTime: z.string().optional(),
          modifiedTime: z.string().optional(),
          parents: z.array(z.string()).optional(),
        }),
      ),
    }),
  )
  .handler(async ({ input }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles(
      input?.folderId,
    );

    return {
      folders: result.folders.map((folder) => ({
        id: folder.id!,
        name: folder.name!,
        createdTime: folder.createdTime || undefined,
        modifiedTime: folder.modifiedTime || undefined,
      })),
      files: result.files.map((file) => ({
        id: file.id!,
        name: file.name!,
        mimeType: file.mimeType || undefined,
        size: file.size || undefined,
        createdTime: file.createdTime || undefined,
        modifiedTime: file.modifiedTime || undefined,
        parents: file.parents || undefined,
      })),
    };
  });

export const getTemplatesFromGoogleDrive = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles();

    const templateFiles = result.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc'),
    );

    const templates = await Promise.all(
      templateFiles.map(async (file) => {
        let placeholders: Array<{
          type: 'text' | 'number' | 'date' | 'boolean';
          required: boolean;
          key: string;
          label: string;
          description?: string;
          defaultValue?: string;
        }> = [];

        const existingTemplate = await prisma.documentTemplate.findFirst({
          where: { googleDriveId: file.id! },
        });

        if (existingTemplate && Array.isArray(existingTemplate.placeholders)) {
          placeholders = existingTemplate.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>;
        }

        return {
          id: file.id!,
          googleDriveId: file.id!,
          fileName: file.name!,
          mimeType: file.mimeType || 'application/octet-stream',
          placeholders,
          createdAt: file.createdTime ? new Date(file.createdTime) : new Date(),
          updatedAt: file.modifiedTime
            ? new Date(file.modifiedTime)
            : new Date(),
        };
      }),
    );

    return templates;
  });

export const syncTemplatesWithGoogleDrive = createServerAction()
  .input(z.object({}).optional())
  .output(
    z.object({
      synced: z.number(),
      updated: z.number(),
      created: z.number(),
    }),
  )
  .handler(async () => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles();

    const templateFiles = result.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc'),
    );

    let created = 0;
    let updated = 0;

    for (const file of templateFiles) {
      const existingTemplate = await prisma.documentTemplate.findFirst({
        where: { googleDriveId: file.id! },
      });

      if (existingTemplate) {
        await prisma.documentTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            fileName: file.name!,
            mimeType: file.mimeType || 'application/octet-stream',
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        updated++;
      } else {
        await prisma.documentTemplate.create({
          data: {
            googleDriveId: file.id!,
            fileName: file.name!,
            mimeType: file.mimeType || 'application/octet-stream',
            placeholders: [],
            createdAt: file.createdTime
              ? new Date(file.createdTime)
              : new Date(),
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        created++;
      }
    }

    revalidatePath('/documents/templates');
    return {
      synced: templateFiles.length,
      updated,
      created,
    };
  });

export const generateDocumentFromTemplate = createServerAction()
  .input(generateDocumentFromTemplateSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    // Obtener la plantilla
    const template = await prisma.documentTemplate.findUnique({
      where: { id: data.templateId },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    // Obtener datos del caso para el contexto
    const caseData = await prisma.case.findUnique({
      where: { id: data.caseId },
      include: {
        debtor: true,
        operator: true,
      },
    });

    if (!caseData) {
      throw new Error('Caso no encontrado');
    }

    // Aquí se implementaría la lógica de generación del documento
    // usando la biblioteca docx-templates para reemplazar placeholders

    const document = await prisma.document.create({
      data: {
        name: data.name,
        type: 'DOCUMENTO_GENERADO',
        status: 'GENERADO',
        url: '', // Se generaría la URL después de guardar el archivo
        caseId: data.caseId,
        templateId: data.templateId,
        isGenerated: true,
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocumentContent = createServerAction()
  .input(updateDocumentContentSchema)
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: data }) => {
    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: data.documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // For now, we'll just update the document name or status
    // In the future, this could upload a new version to Google Drive
    await prisma.document.update({
      where: { id: data.documentId },
      data: {
        name: data.content.substring(0, 100), // Use first 100 chars as name
        status: 'ACTUALIZADO',
      },
    });

    revalidatePath('/documents');
    return { success: true };
  });

export const getDocumentStats = createServerAction()
  .output(documentStatsSchema)
  .handler(async () => {
    const [total, byStatus, byType] = await Promise.all([
      prisma.document.count(),
      prisma.document.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.document.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      total,
      byStatus,
      byType,
    };
  });
