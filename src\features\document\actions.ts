'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';
import { z } from 'zod';
import Docxtemplater from 'docxtemplater';
import <PERSON>z<PERSON><PERSON> from 'pizzip';
import libre from 'libreoffice-convert';
import { promisify } from 'util';

import prisma from '@/lib/prisma';

const libreConvert = promisify(libre.convert);

import {
  createDocumentSchema,
  updateDocumentSchema,
  deleteDocumentSchema,
  documentFilterSchema,
  documentStatsSchema,
  getDocumentsSchema,
  getDocumentByIdSchema,
  createDocumentOutputSchema,
  updateDocumentOutputSchema,
  deleteDocumentOutputSchema,
  generateDocumentFromTemplateSchema,
  updateDocumentContentSchema,
  documentTemplateSchema,
} from './schemas';
import { DocumentGenerationContext } from './types';

export const getDocuments = createServerAction()
  .input(documentFilterSchema.optional())
  .output(getDocumentsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.document.findMany({
      where: {
        ...(filters?.caseId && { caseId: filters.caseId }),
        ...(filters?.type && { type: filters.type }),
        ...(filters?.status && { status: filters.status }),
        ...(filters?.search && {
          OR: [
            { name: { contains: filters.search, mode: 'insensitive' } },
            {
              case: {
                caseNumber: { contains: filters.search, mode: 'insensitive' },
              },
            },
            {
              case: {
                debtorName: { contains: filters.search, mode: 'insensitive' },
              },
            },
          ],
        }),
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });
  });

export const getDocumentById = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID del documento es requerido') }),
  )
  .output(getDocumentByIdSchema)
  .handler(async ({ input: { id } }) => {
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('Documento no encontrado');
    }

    return document;
  });

export const createDocument = createServerAction()
  .input(createDocumentSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const document = await prisma.document.create({
      data: {
        ...data,
        status: data.status ?? 'PENDIENTE',
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocument = createServerAction()
  .input(updateDocumentSchema)
  .output(updateDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    const { id: documentId, ...updateData } = data;
    const document = await prisma.document.update({
      where: { id: documentId },
      data: updateData,
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const deleteDocument = createServerAction()
  .input(deleteDocumentSchema)
  .output(deleteDocumentOutputSchema)
  .handler(async ({ input: { id } }) => {
    const deletedDocument = await prisma.document.delete({
      where: { id },
    });

    revalidatePath('/documents');
    return deletedDocument;
  });

// Acciones para plantillas de documentos
export const getDocumentTemplates = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const templates = await prisma.documentTemplate.findMany({
      orderBy: {
        fileName: 'asc',
      },
    });

    return templates.map((template) => ({
      ...template,
      placeholders: Array.isArray(template.placeholders)
        ? (template.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
    }));
  });

export const updateDocumentTemplate = createServerAction()
  .input(
    z.object({
      id: z.string().min(1, 'El ID de la plantilla es requerido'),
      placeholders: z
        .array(
          z.object({
            key: z.string().min(1, 'La clave es requerida'),
            label: z.string().min(1, 'La etiqueta es requerida'),
          }),
        )
        .optional(),
    }),
  )
  .output(documentTemplateSchema)
  .handler(async ({ input: data }) => {
    const { id, placeholders } = data;

    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const transformedPlaceholders = (placeholders || []).map((p) => ({
      key: p.key,
      label: p.label,
    }));

    const updatedTemplate = await prisma.documentTemplate.update({
      where: { id },
      data: { placeholders: transformedPlaceholders },
    });

    revalidatePath('/documents');
    return {
      id: updatedTemplate.id,
      googleDriveId: updatedTemplate.googleDriveId,
      fileName: updatedTemplate.fileName,
      mimeType: updatedTemplate.mimeType,
      placeholders: Array.isArray(updatedTemplate.placeholders)
        ? (updatedTemplate.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean' | 'email';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>)
        : [],
      createdAt: updatedTemplate.createdAt,
      updatedAt: updatedTemplate.updatedAt,
    };
  });

export const downloadDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(
    z.object({
      buffer: z.any(),
      fileName: z.string(),
      mimeType: z.string(),
    }),
  )
  .handler(async ({ input: { id } }) => {
    const template = await prisma.documentTemplate.findUnique({
      where: { id },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    const { googleDriveService } = await import('@/lib/google-drive');

    const fileInfo = await googleDriveService.getFileInfo(
      template.googleDriveId,
    );

    if (!fileInfo) {
      throw new Error('Plantilla no encontrada en Google Drive');
    }

    const buffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    return {
      buffer,
      fileName: fileInfo.name || template.fileName || 'template.docx',
      mimeType:
        fileInfo.mimeType || template.mimeType || 'application/octet-stream',
    };
  });

export const deleteDocumentTemplate = createServerAction()
  .input(
    z.object({ id: z.string().min(1, 'El ID de la plantilla es requerido') }),
  )
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: { id } }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    try {
      await googleDriveService.deleteFile(id);
    } catch (error) {
      console.error('Error eliminando archivo de Google Drive:', error);
      throw new Error('Error al eliminar la plantilla de Google Drive');
    }

    const existingTemplate = await prisma.documentTemplate.findFirst({
      where: { googleDriveId: id },
    });

    if (existingTemplate) {
      await prisma.documentTemplate.delete({
        where: { id: existingTemplate.id },
      });
    }

    revalidatePath('/documents/templates');
    return { success: true };
  });

export const getGoogleDriveFoldersAndFiles = createServerAction()
  .input(z.object({ folderId: z.string().optional() }).optional())
  .output(
    z.object({
      folders: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          createdTime: z.string().optional(),
          modifiedTime: z.string().optional(),
        }),
      ),
      files: z.array(
        z.object({
          id: z.string(),
          name: z.string(),
          mimeType: z.string().optional(),
          size: z.string().optional(),
          createdTime: z.string().optional(),
          modifiedTime: z.string().optional(),
          parents: z.array(z.string()).optional(),
        }),
      ),
    }),
  )
  .handler(async ({ input }) => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles(
      input?.folderId,
    );

    return {
      folders: result.folders.map((folder) => ({
        id: folder.id!,
        name: folder.name!,
        createdTime: folder.createdTime || undefined,
        modifiedTime: folder.modifiedTime || undefined,
      })),
      files: result.files.map((file) => ({
        id: file.id!,
        name: file.name!,
        mimeType: file.mimeType || undefined,
        size: file.size || undefined,
        createdTime: file.createdTime || undefined,
        modifiedTime: file.modifiedTime || undefined,
        parents: file.parents || undefined,
      })),
    };
  });

export const getTemplatesFromGoogleDrive = createServerAction()
  .input(z.object({}).optional())
  .output(z.array(documentTemplateSchema))
  .handler(async () => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles();

    const templateFiles = result.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc'),
    );

    const templates = await Promise.all(
      templateFiles.map(async (file) => {
        let placeholders: Array<{
          type: 'text' | 'number' | 'date' | 'boolean';
          required: boolean;
          key: string;
          label: string;
          description?: string;
          defaultValue?: string;
        }> = [];

        let existingTemplate = await prisma.documentTemplate.findFirst({
          where: { googleDriveId: file.id! },
        });

        // If template doesn't exist in database, create it
        existingTemplate ??= await prisma.documentTemplate.create({
          data: {
            googleDriveId: file.id!,
            fileName: file.name!,
            mimeType: file.mimeType || 'application/octet-stream',
            placeholders: [],
            createdAt: file.createdTime
              ? new Date(file.createdTime)
              : new Date(),
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });

        if (existingTemplate && Array.isArray(existingTemplate.placeholders)) {
          placeholders = existingTemplate.placeholders as Array<{
            type: 'text' | 'number' | 'date' | 'boolean';
            required: boolean;
            key: string;
            label: string;
            description?: string;
            defaultValue?: string;
          }>;
        }

        return {
          id: existingTemplate.id,
          googleDriveId: file.id!,
          fileName: file.name!,
          mimeType: file.mimeType || 'application/octet-stream',
          placeholders,
          createdAt: file.createdTime ? new Date(file.createdTime) : new Date(),
          updatedAt: file.modifiedTime
            ? new Date(file.modifiedTime)
            : new Date(),
        };
      }),
    );

    return templates;
  });

export const syncTemplatesWithGoogleDrive = createServerAction()
  .input(z.object({}).optional())
  .output(
    z.object({
      synced: z.number(),
      updated: z.number(),
      created: z.number(),
    }),
  )
  .handler(async () => {
    const { googleDriveService } = await import('@/lib/google-drive');

    const result = await googleDriveService.listFoldersAndFiles();

    const templateFiles = result.files.filter(
      (file) =>
        file.mimeType?.includes('word') ||
        file.mimeType?.includes('document') ||
        file.name?.toLowerCase().endsWith('.docx') ||
        file.name?.toLowerCase().endsWith('.doc'),
    );

    let created = 0;
    let updated = 0;

    for (const file of templateFiles) {
      const existingTemplate = await prisma.documentTemplate.findFirst({
        where: { googleDriveId: file.id! },
      });

      if (existingTemplate) {
        await prisma.documentTemplate.update({
          where: { id: existingTemplate.id },
          data: {
            fileName: file.name!,
            mimeType: file.mimeType || 'application/octet-stream',
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        updated++;
      } else {
        await prisma.documentTemplate.create({
          data: {
            googleDriveId: file.id!,
            fileName: file.name!,
            mimeType: file.mimeType || 'application/octet-stream',
            placeholders: [],
            createdAt: file.createdTime
              ? new Date(file.createdTime)
              : new Date(),
            updatedAt: file.modifiedTime
              ? new Date(file.modifiedTime)
              : new Date(),
          },
        });
        created++;
      }
    }

    revalidatePath('/documents/templates');
    return {
      synced: templateFiles.length,
      updated,
      created,
    };
  });

export const generateDocumentFromTemplate = createServerAction()
  .input(generateDocumentFromTemplateSchema)
  .output(createDocumentOutputSchema)
  .handler(async ({ input: data }) => {
    // Obtener la plantilla
    const template = await prisma.documentTemplate.findUnique({
      where: { id: data.templateId },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    // Obtener datos del caso para el contexto
    const caseData = await prisma.case.findUnique({
      where: { id: data.caseId },
      include: {
        debtor: true,
        operator: true,
      },
    });

    if (!caseData) {
      throw new Error('Caso no encontrado');
    }

    // Descargar la plantilla desde Google Drive
    const { googleDriveService } = await import('@/lib/google-drive');
    const templateBuffer = await googleDriveService.downloadFile(
      template.googleDriveId,
    );

    // Preparar datos para reemplazar placeholders
    const context: DocumentGenerationContext = {
      case: {
        id: caseData.id,
        caseNumber: caseData.caseNumber,
        debtorName: caseData.debtorName,
        type: caseData.type,
        status: caseData.status,
        totalDebt: Number(caseData.totalDebt),
        creditors: caseData.creditors,
        createdDate: caseData.createdDate,
        hearingDate: caseData.hearingDate || undefined,
        phase: caseData.phase || undefined,
      },
      debtor: {
        id: caseData.debtor.id,
        name: caseData.debtor.name,
        documentNumber: caseData.debtor.idNumber,
        documentType: caseData.debtor.idType,
        email: caseData.debtor.email || undefined,
        phone: caseData.debtor.phone || undefined,
        address: caseData.debtor.address || undefined,
      },
      operator: {
        id: caseData.operator.id,
        name: caseData.operator.name,
        email: caseData.operator.email,
      },
    };

    // Generar el documento usando docx-templates
    const { DocumentGenerator } = await import('@/lib/document-generator');
    const generatedBuffer = await DocumentGenerator.generateFromTemplate(
      templateBuffer,
      context,
    );

    // Auto-generar nombre del documento
    const templateName = template.fileName.replace(/\.[^/.]+$/, '');
    const documentName = `${templateName} - ${caseData.caseNumber}`;

    // Subir el documento generado a Google Drive
    const generatedFileName = `${documentName}_${new Date().toISOString().split('T')[0]}.docx`;
    const uploadedFileId = await googleDriveService.uploadFile(
      generatedFileName,
      generatedBuffer,
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    );

    const document = await prisma.document.create({
      data: {
        name: documentName,
        type: 'DOCUMENTO_GENERADO',
        status: 'GENERADO',
        url: `https://drive.google.com/file/d/${uploadedFileId}/view`,
        googleDriveId: uploadedFileId,
        caseId: data.caseId,
        templateId: data.templateId,
        isGenerated: true,
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');
    return document;
  });

export const updateDocumentContent = createServerAction()
  .input(updateDocumentContentSchema)
  .output(z.object({ success: z.boolean() }))
  .handler(async ({ input: data }) => {
    // Obtener el documento actual
    const currentDocument = await prisma.document.findUnique({
      where: { id: data.documentId },
    });

    if (!currentDocument) {
      throw new Error('Documento no encontrado');
    }

    // For now, we'll just update the document name or status
    // In the future, this could upload a new version to Google Drive
    await prisma.document.update({
      where: { id: data.documentId },
      data: {
        name: data.content.substring(0, 100), // Use first 100 chars as name
        status: 'ACTUALIZADO',
      },
    });

    revalidatePath('/documents');
    return { success: true };
  });

export const extractPlaceholdersFromTemplate = createServerAction()
  .input(z.object({ templateId: z.string() }))
  .output(
    z.array(
      z.object({
        key: z.string(),
        label: z.string(),
        type: z.enum(['text', 'number', 'date', 'email']),
        required: z.boolean(),
        description: z.string().optional(),
      }),
    ),
  )
  .handler(async ({ input: { templateId } }) => {
    const template = await prisma.documentTemplate.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error('Plantilla no encontrada');
    }

    try {
      // Obtener el archivo de Google Drive usando el servicio existente
      const { googleDriveService } = await import('@/lib/google-drive');
      const templateBuffer = await googleDriveService.downloadFile(
        template.googleDriveId,
      );

      // Extraer placeholders del documento Word
      const placeholders =
        await extractPlaceholdersFromWordDocument(templateBuffer);

      return placeholders;
    } catch (error) {
      console.error('Error extracting placeholders:', error);
      throw new Error('Error al extraer placeholders del documento');
    }
  });

async function extractPlaceholdersFromWordDocument(buffer: Buffer): Promise<
  Array<{
    key: string;
    label: string;
    type: 'text' | 'number' | 'date' | 'email';
    required: boolean;
    description?: string;
  }>
> {
  try {
    const JSZip = (await import('jszip')).default;
    const zip = await JSZip.loadAsync(buffer);

    // Leer el contenido principal del documento
    const documentXml = await zip.file('word/document.xml')?.async('text');
    if (!documentXml) {
      throw new Error('No se pudo leer el contenido del documento');
    }

    const placeholderPatterns = [
      /\{\{([^}]+)\}\}/g,
      /\[([^\]]+)\]/g,
      /%([^%]+)%/g,
    ];

    const foundPlaceholders = new Set<string>();

    for (const pattern of placeholderPatterns) {
      let match;
      while ((match = pattern.exec(documentXml)) !== null) {
        const placeholder = match[1].trim();
        if (placeholder && placeholder.length > 0) {
          foundPlaceholders.add(placeholder);
        }
      }
    }

    // Convertir a formato de placeholder con tipos inferidos
    return Array.from(foundPlaceholders).map((key) => {
      const lowerKey = key.toLowerCase();
      let type: 'text' | 'number' | 'date' | 'email' = 'text';

      if (lowerKey.includes('fecha') || lowerKey.includes('date')) {
        type = 'date';
      } else if (lowerKey.includes('email') || lowerKey.includes('correo')) {
        type = 'email';
      } else if (
        lowerKey.includes('numero') ||
        lowerKey.includes('cantidad') ||
        lowerKey.includes('monto')
      ) {
        type = 'number';
      }

      return {
        key: key.toLowerCase().replace(/\s+/g, '_'),
        label: key.charAt(0).toUpperCase() + key.slice(1),
        type,
        required: false,
      };
    });
  } catch (error) {
    console.error('Error parsing Word document:', error);
    return [];
  }
}

export const getDocumentStats = createServerAction()
  .output(documentStatsSchema)
  .handler(async () => {
    const [total, byStatus, byType] = await Promise.all([
      prisma.document.count(),
      prisma.document.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.document.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      total,
      byStatus,
      byType,
    };
  });

export const generateDocumentWithPlaceholders = createServerAction()
  .input(
    z.object({
      templateId: z.string(),
      caseId: z.string(),
      format: z.enum(['docx', 'pdf']),
    }),
  )
  .output(
    z.object({
      success: z.boolean(),
      message: z.string(),
      data: z
        .object({
          documentId: z.string(),
          fileName: z.string(),
          buffer: z.any(),
          mimeType: z.string(),
        })
        .nullable(),
    }),
  )
  .handler(async ({ input }) => {
    try {
      const { templateId, caseId, format } = input;

      // Get case data with all related information
      const caseData = await prisma.case.findUnique({
        where: { id: caseId },
        include: {
          debtor: true,
          operator: true,
        },
      });

      if (!caseData) {
        throw new Error('Caso no encontrado');
      }

      // Get template data
      const template = await prisma.documentTemplate.findUnique({
        where: { id: templateId },
      });

      if (!template || !template.googleDriveId) {
        throw new Error('Plantilla no encontrada');
      }

      // Download template from Google Drive
      const { googleDriveService } = await import('@/lib/google-drive');
      const templateBuffer = await googleDriveService.downloadFile(
        template.googleDriveId,
      );

      // Prepare data for template replacement
      const templateData = {
        numeroCaso: caseData.caseNumber,
        nombreDeudor: caseData.debtorName,
        tipoCaso: caseData.type,
        estadoCaso: caseData.status,
        fechaCreacion: caseData.createdDate.toLocaleDateString('es-ES'),
        fechaAudiencia: caseData.hearingDate?.toLocaleDateString('es-ES') || '',
        faseCaso: caseData.phase || '',
        tramite: caseData.tramite || '',
        fechaRadicacion: caseData.filingDate?.toLocaleDateString('es-ES') || '',
        abogado: caseData.attorney || '',
        capitalAdeudado: caseData.totalDebt?.toString() || '',
        deudorNombre: caseData.debtor?.name || '',
        deudorEmail: caseData.debtor?.email || '',
        deudorTelefono: caseData.debtor?.phone || '',
        deudorDireccion: caseData.debtor?.address || '',
        deudorCiudad: caseData.debtor?.city || '',
        deudorDepartamento: caseData.debtor?.department || '',
        deudorIngresosMensuales:
          caseData.debtor?.monthlyIncome?.toString() || '',
        deudorGastosMensuales:
          caseData.debtor?.monthlyExpenses?.toString() || '',
        operadorNombre: caseData.operator?.name || '',
        operadorEmail: caseData.operator?.email || '',
      };

      // Process template with docxtemplater
      const zip = new PizZip(templateBuffer);
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      doc.render(templateData);

      const docxBuffer = doc.getZip().generate({
        type: 'nodebuffer',
        compression: 'DEFLATE',
      });

      let finalBuffer = docxBuffer;
      let fileName = `${caseData.caseNumber}_${template.fileName}`;
      let mimeType =
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

      // Convert to PDF if requested
      if (format === 'pdf') {
        try {
          finalBuffer = await libreConvert(docxBuffer, '.pdf', undefined);
          fileName = fileName.replace('.docx', '.pdf');
          mimeType = 'application/pdf';
        } catch (error) {
          console.error('Error converting to PDF:', error);
          throw new Error('Error al convertir el documento a PDF');
        }
      }

      // Save generated document to database
      const generatedDocument = await prisma.document.create({
        data: {
          name: fileName,
          type: format.toUpperCase(),
          status: 'GENERADO',
          url: `#generated-${Date.now()}`, // Temporary URL for generated documents
          caseId: caseData.id,
          templateId: template.id,
          isGenerated: true,
        },
      });

      revalidatePath('/documents');

      return {
        success: true,
        message: 'Documento generado exitosamente',
        data: {
          documentId: generatedDocument.id,
          fileName,
          buffer: finalBuffer,
          mimeType,
        },
      };
    } catch (error) {
      console.error('Error generating document:', error);
      return {
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'Error al generar el documento',
        data: null,
      };
    }
  });
